@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.settings-button {
  .ui-icon {
    width: 17px;
    height: 17px;

    >* {
      width: 17px;
      height: 17px;
      fill: var(--color-guide-brand-main-foreground);
    }
  }

  // fluent-uiのデフォルト挙動だとhover時常に内部が塗られてしまうので
  // スタイルの挙動を上書く

  // 通常時のhover
  &:hover,
  &.ui-button:hover {
    .ui-icon__outline {
      display: block;
    }

    .ui-icon__filled {
      display: none;
    }
  }

  // active時（塗りつぶし表示）
  &.is-active,
  &.ui-button.is-active {
    .ui-icon__outline {
      display: none;
    }

    .ui-icon__filled {
      display: block;
    }
  }
}
