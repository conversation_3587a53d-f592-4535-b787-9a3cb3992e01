@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.simple-modal {
  display: flex;
  flex-flow: column;
  position: relative;

  &.is-self-height {
    height: 100%;
  }

  @include media-pc {
    background-color: var(--color-guide-background-1);
    border-radius: 4px;
    box-shadow: $box-shadow-2;
  }
}

.simple-modal-edge {
  @include media-pc {
    display: none;
  }
}

.simple-modal-close-pc {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 1;

  &:hover {
    .ui-icon__filled {
      fill: var(--color-guide-brand-icon-hover);
    }
  }

  @include media-sp {
    display: none;
  }
}

.simple-modal-scroll-wrapper {
  height: 100%;
  display: flex;
  flex-flow: column;
  box-shadow: $box-shadow-2;

  @include media-sp {
    background-color: var(--color-guide-background-1);
  }

  @include media-pc {
    border-radius: 4px;
    overflow: hidden;
    padding-top: 40px;
  }
}

.simple-modal-scroll-inner {
  display: flex;
  flex-flow: column;
  height: 100%;
  padding: 24px;

  @include media-sp {
    padding-top: 12px;
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }
}

.simple-modal-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.simple-modal-title {
  font-size: 24px;
  margin: 0;

  @include media-sp {
    font-size: 16px;
  }
}

.simple-modal-content {
  flex: 1;
  
  p {
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    color: var(--color-guide-foreground-1);
  }
}
