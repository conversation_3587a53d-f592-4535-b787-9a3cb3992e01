import * as React from 'react';
import { <PERSON><PERSON>, Head<PERSON> } from '@fluentui/react-northstar';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';

// CSS
import './SimpleModal.scss';

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  title?: string;
  content?: string;
  onClose?: () => void;
}

/**
 * SimpleModal
 * @param props
 */
const SimpleModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    title = 'Teams設定',
    content = 'これは簡単なモーダルです。Teams設定に関する内容がここに表示されます。',
    onClose,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    return mergedClassName(isOpen, step1);
  }, [className, open]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={title} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>{content}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleModal;
